import CalendarList from '@/components/CalendarList';
import CalendarSelection from '@/components/CalendarSelection';
import HeaderDashboard from '@/components/common/HeaderDashboard';
import TodayComponent from '@/components/common/TodayComponent';
import UpcomingTrips from '@/components/UpcomingTrips';
import { getUserProfile } from '@/methods/users';
import { getUpcomingTrips, getPastTrips } from '@/methods/trips';
import { ScrollView, View } from 'react-native';
import SetYourNextTrip from '@/components/SetYourNextTrip';

export default function HomeScreen() {
  const { data: userProfile } = getUserProfile();
  const { data: upcomingTrips } = getUpcomingTrips();
  const { data: pastTrips } = getPastTrips();

  const profile = userProfile?.data.profile;
  const firstName = profile?.name ? profile.name.split(' ')[0] : '';
  const displayName = firstName || profile?.userName || profile?.email;

  // Check if there are upcoming trips
  const hasUpcomingTrips = upcomingTrips?.data?.events?.length > 0;

  // Check if there are past trips
  const hasPastTrips = pastTrips?.data?.events?.length > 0;

  const hasNoTrip = !hasUpcomingTrips && !hasPastTrips;

  return (
    <View>
      <HeaderDashboard title={`Welcome, ${displayName}!`} />
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={{ paddingBottom: 220 }}
      >
        {/* <TagLocation /> */}
        <TodayComponent />
        {hasNoTrip ?
          <View>
            <CalendarSelection />
            <View style={{ marginTop: 16 }}>
              <SetYourNextTrip />
            </View>
          </View> : <View>
            <CalendarSelection />
            <CalendarList />
            <UpcomingTrips />
          </View>}
      </ScrollView>
    </View>
  );
}
