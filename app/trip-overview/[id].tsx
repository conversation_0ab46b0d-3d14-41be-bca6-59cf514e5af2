import { PackingList } from '@/components/Packinglist';
import { StyledDiary } from '@/components/Packinglist/style-diary';
import TripCard from '@/components/TripCard';
import Button from '@/components/common/Button';
import HeaderDashboard from '@/components/common/HeaderDashboardTrip';
import { useSession } from '@/config/ctx';
import { getPackingList, getTripById } from '@/methods/trips';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import { NestableScrollContainer } from 'react-native-draggable-flatlist';

interface ClothesItem {
  id: string;
  name: string;
  isActive: boolean;
  type: 'clothes' | 'category';
}

interface PackingList {
  id: string;
  name: string;
  isActive: boolean;
  type?: 'clothes' | 'category';
  items?: ClothesItem[];
}

export default function TripOverview() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { data: trip, isLoading: isLoadingTrip } = getTripById(id as string);
  const { data: packingLists } = getPackingList(id as string);
  const tripData = trip?.data?.event;

  const [activeTab, setActiveTab] = useState('Packing List');

  return (
    <View style={{ flexGrow: 1, flexBasis: '100%' }}>
      <View style={{ flex: 1 }}>
        <View style={{ marginBottom: 10 }}>
          <HeaderDashboard
            title="Trip Details"
            backButton={true}
            onBackPress={() => {
              router.push('/home');
            }}
          />
        </View>
        <View style={{ marginTop: 10 }}>
          <TripCard
            trip={tripData}
            notClickable
            isDeletable={true}
            onDelete={() => {
              router.push('/home');
            }}
            isLoading={isLoadingTrip}
            packingListItems={packingLists?.data?.packingList || []}
          />
        </View>
        <View style={{ marginTop: 20 }}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            bounces={false}
            contentContainerStyle={{ gap: 10 }}
            style={{ flexDirection: 'row', }}
          >
            <Button
              isNav
              isActive={activeTab === 'Packing List'}
              title="Packing List"
              onPress={() => {
                setActiveTab('Packing List');
              }}
            />
            <Button
              isNav
              isActive={activeTab === 'style-diary'}
              title="Style Diary"
              onPress={() => setActiveTab('style-diary')}
            />
            <Button isNav title="Luggage" onPress={() => { }} />
          </ScrollView>
        </View>
        {activeTab === 'Packing List' && <PackingList id={id as string} />}
        {activeTab === 'style-diary' && (
          <StyledDiary eventId={id as string} />
        )}

      </View>
    </View>
  );
}
