import React, { useCallback, useRef, useState, useMemo, forwardRef, useImperativeHandle } from 'react';
import {
  FlatList,
  Modal,
  Platform,
  TouchableWithoutFeedback,
  View,
  Text,
  StyleProp,
  ViewStyle,
} from 'react-native';
import Check from '@/assets/svg/check.svg';
import {
  DropdownContainer,
  DropdownItem,
  DropdownOverlay,
  DropdownText,
  SelectedText,
  CheckView,
} from './styles';

export interface DropdownOption {
  label: string;
  value: string;
}

interface GenericDropdownProps {
  options: DropdownOption[];
  selectedValue?: string;
  onSelect: (option: DropdownOption) => void;
  placeholder?: string;
  width?: string | number;
  height?: number;
  buttonStyles?: StyleProp<ViewStyle>;
  // External control props
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
}

export interface GenericDropdownRef {
  open: () => void;
  close: () => void;
  toggle: () => void;
}

const GenericDropdown = forwardRef<GenericDropdownRef, GenericDropdownProps>(({
  options,
  selectedValue,
  onSelect,
  placeholder = 'Select option',
  width = '60%',
  height = 30,
  buttonStyles,
  isOpen: externalIsOpen,
  onOpenChange,
}, ref) => {
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const [showAbove, setShowAbove] = useState(false);
  const touchableRef = useRef<View>(null);
  const [position, setPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });

  // Use external state if provided, otherwise use internal state
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const setIsOpen = useCallback((value: boolean) => {
    if (externalIsOpen !== undefined) {
      onOpenChange?.(value);
    } else {
      setInternalIsOpen(value);
    }
  }, [externalIsOpen, onOpenChange]);

  const selectedOption = options.find(option => option.value === selectedValue);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
    toggle: () => setIsOpen(!isOpen),
  }), [setIsOpen, isOpen]);

  const handleSelect = useCallback((option: DropdownOption) => {
    setIsOpen(false); // Close immediately
    onSelect(option);
  }, [setIsOpen, onSelect]);

  const measureTouchable = useCallback(() => {
    if (!touchableRef.current) return;

    touchableRef.current?.measure((_fx, _fy, width, height, px, py) => {
      const windowHeight = 800; // Approximate window height
      const maxDropdownHeight = Math.min(options.length, 5) * 44; // 44px per item, max 5 items
      const spaceBelow = windowHeight - py - height;
      const shouldShowAbove = spaceBelow < maxDropdownHeight + 40;

      setShowAbove(shouldShowAbove);
      setPosition({
        x: px,
        y: py,
        width,
        height,
      });
    });
  }, [options]);

  const handleToggle = useCallback(() => {
    measureTouchable();
    setIsOpen(!isOpen);
  }, [measureTouchable, setIsOpen, isOpen]);

  const renderItem = ({ item }: { item: DropdownOption }) => (
    <DropdownItem
      key={`${item.value}-${selectedValue === item.value}`}
      onPress={() => handleSelect(item)}
      activeOpacity={0.7}
    >
      {selectedValue === item.value && (
        <CheckView>
          <Check />
        </CheckView>
      )}
      <DropdownText>{item.label}</DropdownText>
    </DropdownItem>
  );

  return (
    <>
      <DropdownContainer
        ref={touchableRef}
        onLayout={measureTouchable}
        onPress={handleToggle}
        activeOpacity={0.8}
        width={width}
        height={height}
        style={buttonStyles}
      >
        <SelectedText numberOfLines={1}>
          {selectedOption?.label || placeholder}
        </SelectedText>
        <Text style={{ fontSize: 16, color: '#666' }}></Text>
      </DropdownContainer>

      {isOpen && (
        <Modal
          visible={isOpen}
          transparent
          animationType="none"
          statusBarTranslucent={Platform.OS === 'android'}
        >
          <TouchableWithoutFeedback onPress={() => setIsOpen(false)}>
            <DropdownOverlay>
              <View
                style={{
                  position: 'absolute',
                  top: showAbove
                    ? position.y - Math.min(options.length, 5) * 44
                    : position.y + position.height,
                  left: position.x,
                  width: position.width,
                  maxHeight: 5 * 44,
                  backgroundColor: '#fff',
                  borderRadius: 8,
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  overflow: 'hidden',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.15,
                  shadowRadius: 3.84,
                  elevation: 5,
                }}
                onStartShouldSetResponder={() => true}
              >
                <FlatList
                  key={`dropdown-${selectedValue}`}
                  data={options}
                  keyExtractor={(item) => item.value}
                  renderItem={renderItem}
                  style={{ maxHeight: 5 * 44 }}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled
                  keyboardShouldPersistTaps="handled"
                  bounces={Platform.OS === 'ios'}
                  overScrollMode={Platform.OS === 'android' ? 'never' : 'auto'}
                  removeClippedSubviews={Platform.OS === 'android'}
                />
              </View>
            </DropdownOverlay>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </>
  );
});

GenericDropdown.displayName = 'GenericDropdown';

export default GenericDropdown; 