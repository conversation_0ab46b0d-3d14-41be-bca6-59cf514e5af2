import React, { useEffect, useRef, useMemo, useState } from 'react';
import { Dimensions, StyleSheet, Modal, View, TouchableWithoutFeedback, StatusBar, Platform, ActivityIndicator, BackHandler } from 'react-native';
import { ChevronLeft } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS
} from 'react-native-reanimated';
import { CategoryItem, getCurrentUserGender } from '@/data/categories';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getGenderSpecificCategories } from '@/utils/categoryUtils';
import { getUserProfile } from '@/methods/users';
import { getStandardizedCategories, formatCategoriesForDisplay } from '@/utils/standardCategories';
import { getClothes } from '@/methods/cloths';

// Get screen dimensions
const { width } = Dimensions.get('window');
const DRAWER_WIDTH = width * 0.7; // 70% of screen width to match design

interface SideFilterPanelProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectCategory: (categoryId: string, categoryName: string) => void;
  selectedCategory: string;
  activeTab: string; // Changed to string to accept any tab name
}

export default function SideFilterPanel({
  isVisible,
  onClose,
  onSelectCategory,
  selectedCategory,
  activeTab
}: SideFilterPanelProps) {
  // Animation value for the drawer position
  const translateX = useSharedValue(DRAWER_WIDTH);

  // Track if animation is complete for cleanup
  const animationComplete = useRef(true);

  // State for gender-specific categories
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);

  // Create a custom "View All" category
  const viewAllCategory: CategoryItem = { id: 'all', name: 'View All' };

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Fetch all items to determine which categories have items
  const { data: allItemsData } = getClothes();

  // Helper function to extract unique categories from items
  const getUsedCategories = (items: any[]): Set<string> => {
    console.log('\n🔍 SIDE FILTER PANEL - ANALYZING USED CATEGORIES 🔍');
    const usedCategories = new Set<string>();

    if (!items || !Array.isArray(items)) {
      console.log('❌ No items or items is not an array');
      return usedCategories;
    }

    console.log('📊 Total items to analyze:', items.length);

    items.forEach((item, index) => {
      console.log(`Item ${index + 1}:`, {
        id: item._id,
        name: item.name,
        category: item.category,
        categoryName: item.categoryName,
        itemCategoryId: item.itemCategoryId
      });

      let categoryFound = false;

      // First, try to get category from item.category.name
      if (item?.category?.name) {
        const categoryName = item.category.name.toLowerCase().trim();
        usedCategories.add(categoryName);
        console.log(`✅ Added category from category.name: "${categoryName}"`);
        categoryFound = true;
      }

      // If no category.name, try to extract from itemCategoryId
      if (!categoryFound && item?.itemCategoryId) {
        let categoryName = '';

        // Handle basic- prefixed categories
        if (item.itemCategoryId.startsWith('basic-')) {
          categoryName = item.itemCategoryId.replace('basic-', '');
        } else if (item.itemCategoryId.startsWith('standard-')) {
          categoryName = item.itemCategoryId.replace('standard-', '');
        } else {
          categoryName = item.itemCategoryId;
        }

        categoryName = categoryName.toLowerCase().trim();
        usedCategories.add(categoryName);
        console.log(`✅ Added category from itemCategoryId: "${categoryName}" (original: "${item.itemCategoryId}")`);
        categoryFound = true;
      }

      // If still no category found, try categoryName field
      if (!categoryFound && item?.categoryName) {
        const categoryName = item.categoryName.toLowerCase().trim();
        usedCategories.add(categoryName);
        console.log(`✅ Added category from categoryName: "${categoryName}"`);
        categoryFound = true;
      }

      if (!categoryFound) {
        console.log(`❌ Item ${index + 1} has no category information`);
      }
    });

    console.log('📋 Final used categories:', Array.from(usedCategories));
    return usedCategories;
  };

  // Load gender-specific categories
  useEffect(() => {
    const loadCategories = async () => {
      setIsLoadingCategories(true);
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('SideFilterPanel - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log('SideFilterPanel - Using gender from getCurrentUserGender:', gender);
        }

        if (!gender) {
          console.warn('SideFilterPanel - No gender available, using default categories');
          const fallbackCategories = [
            viewAllCategory,
            { id: 'tops', name: 'Tops' },
            { id: 'bottoms', name: 'Bottoms' },
            { id: 'dresses', name: 'Dresses' },
            { id: 'shoes', name: 'Shoes' },
            { id: 'accessories', name: 'Accessories' },
            { id: 'loungewear', name: 'Loungewear' } // Added Loungewear
          ];
          setCategories(fallbackCategories);
          return;
        }

        console.log('\n🔍 SIDE FILTER PANEL - LOADING STANDARDIZED CATEGORIES 🔍');

        // Get standardized categories that include both backend and standard categories
        const standardizedCategories = await getStandardizedCategories(gender);

        console.log(`Loaded ${standardizedCategories.length} standardized categories`);

        // Format categories for display in the SideFilterPanel
        // We'll use our custom viewAllCategory instead of the default "All" category
        const formattedCategories = formatCategoriesForDisplay(standardizedCategories, false);

        // Get categories that have items in them
        console.log('🔍 Raw allItemsData:', allItemsData);
        console.log('🔍 Items array:', allItemsData?.items);
        console.log('🔍 Items count:', allItemsData?.items?.length);

        const usedCategories = getUsedCategories(allItemsData?.items || []);

        console.log('🔍 Filtering categories with items...');
        console.log('Available formatted categories:', formattedCategories.map(c => ({ id: c.id, name: c.name })));

        // Filter categories to show only those that have items
        const categoriesWithItems = formattedCategories.filter((category) => {
          // Always include the "View All" category (it will be added later)
          if (category.id === 'all') {
            console.log('✅ Including "View All" category');
            return true;
          }

          // Check if this category has items
          const categoryNameLower = category.name.toLowerCase().trim();
          const hasItems = usedCategories.has(categoryNameLower);
          console.log(`${hasItems ? '✅' : '❌'} Category "${category.name}" (${categoryNameLower}) ${hasItems ? 'has' : 'has no'} items`);
          return hasItems;
        });

        console.log('📋 Final categories with items:', categoriesWithItems.map(c => ({ id: c.id, name: c.name })));

        // Add legacy categories for items that might still use old category system
        const legacyCategories = [
          { id: 'legacy-tops', name: 'Tops' },
          { id: 'legacy-bottoms', name: 'Bottoms' },
          { id: 'legacy-dresses', name: 'Dresses' },
          { id: 'legacy-shoes', name: 'Shoes' },
          { id: 'legacy-accessories', name: 'Accessories' }
        ];

        // Check which legacy categories have items
        const legacyCategoriesWithItems = legacyCategories.filter((category) => {
          const categoryNameLower = category.name.toLowerCase().trim();
          const hasItems = usedCategories.has(categoryNameLower);
          console.log(`${hasItems ? '✅' : '❌'} Legacy category "${category.name}" (${categoryNameLower}) ${hasItems ? 'has' : 'has no'} items`);
          return hasItems;
        });

        // Combine new categories with legacy categories that have items
        const allCategoriesWithItems = [...categoriesWithItems, ...legacyCategoriesWithItems];

        // Remove duplicates by category name (keep the first occurrence)
        const uniqueCategories = allCategoriesWithItems.filter((category, index, array) => {
          return array.findIndex(c => c.name.toLowerCase() === category.name.toLowerCase()) === index;
        });

        console.log('📋 Categories after deduplication:', uniqueCategories.map(c => ({ id: c.id, name: c.name })));

        // Sort categories alphabetically by name (excluding the "View All" category)
        uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));

        // Add our custom viewAllCategory as the first item
        uniqueCategories.unshift(viewAllCategory);

        // Log the categories that will be displayed
        console.log('=== CATEGORIES DISPLAYED IN SIDE FILTER PANEL ===');
        uniqueCategories.forEach((cat, index) => {
          console.log(`${index + 1}. ${cat.name} (ID: ${cat.id})`);
        });
        console.log('=== END DISPLAYED CATEGORIES ===');

        setCategories(uniqueCategories);
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to default categories if there's an error
        // Instead of just showing viewAllCategory, show a basic set of categories
        const fallbackCategoriesData = [
          { id: 'tops', name: 'Tops' },
          { id: 'bottoms', name: 'Bottoms' },
          { id: 'dresses', name: 'Dresses' },
          { id: 'shoes', name: 'Shoes' },
          { id: 'accessories', name: 'Accessories' }
        ];

        // Sort fallback categories alphabetically
        fallbackCategoriesData.sort((a, b) => a.name.localeCompare(b.name));

        // Add viewAllCategory as the first item
        const fallbackCategories = [viewAllCategory, ...fallbackCategoriesData];
        setCategories(fallbackCategories);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    loadCategories();
  }, [userProfile, allItemsData]);

  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        if (isVisible) {
          onClose();
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  // Update animation when visibility changes
  useEffect(() => {
    if (isVisible) {
      // Opening the drawer
      animationComplete.current = false;
      translateX.value = withTiming(
        0,
        {
          duration: 250, // Faster animation to match other components
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        }
      );
    } else {
      // Closing the drawer
      translateX.value = withTiming(
        DRAWER_WIDTH,
        {
          duration: 250,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        },
        (finished) => {
          if (finished) {
            runOnJS(completeAnimation)();
          }
        }
      );
    }
  }, [isVisible]);

  // Function to mark animation as complete
  const completeAnimation = () => {
    animationComplete.current = true;
  };

  // Create animated style for the drawer
  const drawerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  // Handle category selection
  const handleCategorySelect = (category: CategoryItem) => {
    onSelectCategory(category.id, category.name);
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.backdrop} />
        </TouchableWithoutFeedback>

        <Animated.View style={[styles.drawer, drawerAnimatedStyle]}>
          <View style={styles.header}>
            <TouchableWithoutFeedback onPress={onClose}>
              <View style={styles.closeButton}>
                <ChevronLeft size={24} color="#0E7E61" />
              </View>
            </TouchableWithoutFeedback>
            <Animated.Text style={styles.headerTitle}>
              {activeTab === 'Clothes' ? 'Categories' : 'Outfits'}
            </Animated.Text>
          </View>

          <Animated.ScrollView
            style={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 40 }}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
          >
            {activeTab === 'Clothes' ? (
              isLoadingCategories ? (
                // Show loading indicator while categories are loading
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#0E7E61" />
                  <Animated.Text style={styles.loadingText}>
                    Loading categories...
                  </Animated.Text>
                </View>
              ) : (
                // Show categories for Clothes tab
                categories.map((category) => {
                  // Check if this category is selected (handle both name and id matching)
                  // Add null/undefined checks to prevent errors
                  const isSelected =
                    selectedCategory === category.name ||
                    (category.id === 'all' && (selectedCategory === 'All' || selectedCategory === 'View All Clothing')) ||
                    (typeof selectedCategory === 'string' &&
                     typeof category.name === 'string' &&
                     selectedCategory.toLowerCase() === category.name.toLowerCase());

                  return (
                    <TouchableWithoutFeedback
                      key={category.id}
                      onPress={() => handleCategorySelect(category)}
                    >
                      <View style={[
                        styles.categoryItem,
                        isSelected && styles.selectedCategoryItem
                      ]}>
                        <Animated.Text style={[
                          styles.categoryText,
                          isSelected && styles.selectedCategoryText
                        ]}>
                          {category.name}
                        </Animated.Text>
                      </View>
                    </TouchableWithoutFeedback>
                  );
                })
              )
            ) : (
              // Show message for Outfits tab
              <View style={styles.emptyStateContainer}>
                <Animated.Text style={styles.emptyStateText}>
                  No outfit categories available yet.
                </Animated.Text>
              </View>
            )}
          </Animated.ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
}

// Get the status bar height
const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)', // Increased opacity to backdrop
  },
  drawer: {
    width: DRAWER_WIDTH,
    height: '100%',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
    paddingTop: STATUSBAR_HEIGHT, // Add padding for status bar
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    position: 'relative',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  headerTitle: {
    fontFamily: 'MuktaVaani',
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    color: '#1C1C1C',
  },
  closeButton: {
    position: 'absolute',
    left: 0,
    padding: 10,
    zIndex: 1,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  categoryItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    backgroundColor: 'transparent',
  },
  selectedCategoryItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#0E7E61',
  },
  categoryText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 16,
    color: '#1C1C1C',
    fontWeight: 'normal',
  },
  selectedCategoryText: {
    color: '#0E7E61',
    fontWeight: '600',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  emptyStateText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 16,
    color: '#767676',
    textAlign: 'center',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  loadingText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 14,
    color: '#666',
    marginTop: 10,
  },
});
