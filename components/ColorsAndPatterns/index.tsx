import { packingList as packingListString } from '@/constants/strings';
import { Check, ChevronLeft } from 'lucide-react-native';
import { useMemo, useState } from 'react';
import { FlatList, TouchableOpacity, View } from 'react-native';
import { useTheme } from 'styled-components';
import { HeaderText } from '../AddItems/styles';
import {
  ColorsContainer,
  ColorsView,
  FlatListColorContainer,
  FlatListContainer,
  FlatListItem,
  FlatListName,
  FlatListSeparator,
  ImageContainer,
  ModalContainer,
  ModalContent,
  ModalHeader,
  ModalTextTitle,
  ModalTouchableOpacity,
  ViewMoreButton,
  ViewMoreText
} from './style';
interface selectedColor {
  _id: string,
  name: string,
  hexCode: string,
  type: string,
  createdAt: string
}

interface ColorsAndPatternsListProps {
  colorPatternData: any[]
  numColumns?: number
  lengthToDisplay?: number
  isColor?: boolean
  isPattern?: boolean
  showViewMoreButton?: boolean
  buttonPress?: (item: selectedColor | null) => void
  secondaryColor?: any[]
  isSelected?: selectedColor | null
}

export default function ColorsAndPatternsList({
  colorPatternData,
  numColumns = 6,
  lengthToDisplay = 12,
  isColor = false,
  isPattern = false,
  showViewMoreButton = true,
  secondaryColor = [],
  buttonPress = () => { },
  isSelected = {} as selectedColor
}: ColorsAndPatternsListProps) {

  const theme = useTheme()
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any | null>(isSelected);

  const renderItem = ({ item, index }: any) => {
    if (index >= lengthToDisplay) return null;
    const isSelectedItem = selectedItem?._id === item._id;
    const borderColor = isSelectedItem ? theme.brand.green[500] : theme.brand.gray[500];
    const borderWidth = isSelectedItem ? 3 : 1;
    return (
      <View style={{ flex: 1 }}>
        <ColorsContainer>
          {isColor && <ColorsView style={{ backgroundColor: item.hexCode, borderColor, borderWidth }} />}
          {isPattern && <ImageContainer source={{ uri: item.imageURL }} style={{ borderColor, borderWidth }} />}
        </ColorsContainer>
      </View>
    )
  }

  const sortedData = useMemo(() => {
    const combined = [...colorPatternData, ...secondaryColor];
    if (!isSelected?._id) return combined;

    const selectedIndex = combined.findIndex(item => item._id === isSelected._id);
    if (selectedIndex === -1) return combined;

    const selectedItem = combined[selectedIndex];
    const rest = combined.filter(item => item._id !== isSelected._id);
    return [selectedItem, ...rest];
  }, [colorPatternData, secondaryColor, isSelected]);

  return (
    <>
      <FlatList
        keyExtractor={(item, index) => `${item._id}-${index}`}
        data={sortedData}
        numColumns={numColumns}
        renderItem={renderItem}
      />
      {showViewMoreButton && (
        <ViewMoreButton onPress={() => setIsModalVisible(true)}>
          <ViewMoreText>{packingListString.viewMore}</ViewMoreText>
        </ViewMoreButton>
      )}
      <ModalContainer isVisible={isModalVisible} onBackdropPress={() => setIsModalVisible(false)}>
        <ModalContent>
          <ModalHeader>
            <TouchableOpacity onPress={() => setIsModalVisible(false)}>
              <ChevronLeft size={24} color={theme.brand.green[500]} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              setIsModalVisible(false);
              buttonPress(selectedItem);
            }}>
              <HeaderText style={{ textAlign: 'center' }}>{packingListString.save}</HeaderText>
            </TouchableOpacity>
          </ModalHeader>
          <View style={{ paddingBottom: 20 }}>
            <ModalTextTitle>{isColor ? packingListString.colorList : packingListString.patternList}</ModalTextTitle>
          </View>
          <FlatList
            keyExtractor={(item, index) => `${item._id}-${index}`}
            data={[...colorPatternData, ...secondaryColor]}
            showsVerticalScrollIndicator={false}
            renderItem={({ item }) => {
              return (
                <FlatListContainer>
                  <FlatListItem>
                    <ModalTouchableOpacity
                      isSelected={selectedItem?._id === item._id}
                      onPress={() => selectedItem?._id === item._id ? setSelectedItem(null) : setSelectedItem(item)}
                    >
                      {selectedItem?._id === item._id && <Check size={14} color={theme.brand.gray[100]} />}
                    </ModalTouchableOpacity>
                    <FlatListItem>
                      <FlatListColorContainer>
                        {isColor && <ColorsView style={{ backgroundColor: item.hexCode }} />}
                        {isPattern && <ImageContainer source={{ uri: item.imageURL }} />}
                      </FlatListColorContainer>
                    </FlatListItem>
                    <FlatListName>{item.name}</FlatListName>
                  </FlatListItem>
                  <FlatListSeparator />
                </FlatListContainer>
              )
            }}
          />
        </ModalContent>
      </ModalContainer>
    </>
  );
}
