import React from 'react';
import { Dimensions, Image, StyleSheet } from 'react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { useAnimatedG<PERSON>ureHandler } from 'react-native-reanimated';
import { runOnJS } from 'react-native-reanimated';

const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.5; // Aspect ratio of 2:3

interface DraggableResizeableItemProps {
  source: any;
  initialX?: number;
  initialY?: number;
  initialWidth?: number;
  initialHeight?: number;
  onPositionChange?: (position: { x: number; y: number }) => void;
  onSizeChange?: (size: { width: number; height: number }) => void;
  onSelect?: () => void;
  isSelected?: boolean;
}

export const DraggableResizeableItem = ({
  source,
  initialX = 0,
  initialY = 0,
  initialWidth = 100,
  initialHeight = 100,
  onPositionChange,
  onSizeChange,
  onSelect,
  isSelected = false,
}: DraggableResizeableItemProps) => {
  // Shared values for position and size
  const translateX = useSharedValue(initialX);
  const translateY = useSharedValue(initialY);
  const itemWidth = useSharedValue(initialWidth);
  const itemHeight = useSharedValue(initialHeight);

  // Shared values for resize handle positions
  const topLeftX = useSharedValue(-10);
  const topLeftY = useSharedValue(-10);
  const topRightX = useSharedValue(initialWidth - 10);
  const topRightY = useSharedValue(-10);
  const bottomLeftX = useSharedValue(-10);
  const bottomLeftY = useSharedValue(initialHeight - 10);
  const bottomRightX = useSharedValue(initialWidth - 10);
  const bottomRightY = useSharedValue(initialHeight - 10);

  // Update resize handle positions when item size changes
  const updateHandlePositions = () => {
    'worklet';
    topLeftX.value = -10;
    topLeftY.value = -10;
    topRightX.value = itemWidth.value - 10;
    topRightY.value = -10;
    bottomLeftX.value = -10;
    bottomLeftY.value = itemHeight.value - 10;
    bottomRightX.value = itemWidth.value - 10;
    bottomRightY.value = itemHeight.value - 10;
  };

  // Gesture handler for dragging
  const panGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      'worklet';
      // Store initial position
      context.startX = translateX.value;
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      'worklet';
      // Update position based on gesture
      translateX.value = context.startX + event.translationX;
      translateY.value = context.startY + event.translationY;
    },
    onEnd: () => {
      'worklet';
      // Apply spring animation for smooth finish
      translateX.value = withSpring(translateX.value);
      translateY.value = withSpring(translateY.value);

      // Notify parent component of position change
      if (onPositionChange) {
        runOnJS(onPositionChange)({
          x: translateX.value,
          y: translateY.value,
        });
      }
    },
  });

  // Create a resize handle component with animated position
  const ResizeHandle = ({ position }) => {
    // Get the appropriate position values based on the handle position
    let handleX, handleY;

    switch (position) {
      case 'topLeft':
        handleX = topLeftX;
        handleY = topLeftY;
        break;
      case 'topRight':
        handleX = topRightX;
        handleY = topRightY;
        break;
      case 'bottomLeft':
        handleX = bottomLeftX;
        handleY = bottomLeftY;
        break;
      case 'bottomRight':
        handleX = bottomRightX;
        handleY = bottomRightY;
        break;
    }

    // Animated style for the handle position
    const handleStyle = useAnimatedStyle(() => {
      'worklet';
      return {
        transform: [
          { translateX: handleX.value },
          { translateY: handleY.value },
        ],
      };
    });

    // Gesture handler for resizing
    const resizeGestureHandler = useAnimatedGestureHandler({
      onStart: (_, context) => {
        'worklet';
        // Store initial values
        context.startWidth = itemWidth.value;
        context.startHeight = itemHeight.value;
        context.startX = translateX.value;
        context.startY = translateY.value;
      },
      onActive: (event: any, context: any) => {
        'worklet';

        // Calculate deltas from the start position
        const deltaX = event.translationX;
        const deltaY = event.translationY;

        // Calculate new dimensions and positions based on which handle is being dragged
        let newWidth = context.startWidth;
        let newHeight = context.startHeight;
        let newX = context.startX;
        let newY = context.startY;

        switch (position) {
          case 'topLeft':
            newWidth = context.startWidth - deltaX;
            newHeight = context.startHeight - deltaY;
            newX = context.startX + deltaX;
            newY = context.startY + deltaY;
            break;
          case 'topRight':
            newWidth = context.startWidth + deltaX;
            newHeight = context.startHeight - deltaY;
            newY = context.startY + deltaY;
            break;
          case 'bottomLeft':
            newWidth = context.startWidth - deltaX;
            newHeight = context.startHeight + deltaY;
            newX = context.startX + deltaX;
            break;
          case 'bottomRight':
            newWidth = context.startWidth + deltaX;
            newHeight = context.startHeight + deltaY;
            break;
        }

        // Ensure minimum dimensions
        if (newWidth >= 50) {
          itemWidth.value = newWidth;
          translateX.value = newX;
        }

        if (newHeight >= 50) {
          itemHeight.value = newHeight;
          translateY.value = newY;
        }

        // Update handle positions
        updateHandlePositions();

        // Notify parent component of size change
        // if (onSizeChange) {
        //   runOnJS(onSizeChange)({
        //     width: itemWidth.value,
        //     height: itemHeight.value,

        //   });
        // }
      },
      onEnd: () => {
        'worklet';
        // Apply spring animation for smooth finish
        itemWidth.value = withSpring(itemWidth.value);
        itemHeight.value = withSpring(itemHeight.value);
        if (onSizeChange) {
          runOnJS(onSizeChange)({
            width: itemWidth.value,
            height: itemHeight.value,
          });
        }
      },
    });

    return (
      <PanGestureHandler onGestureEvent={resizeGestureHandler}>
        <Animated.View
          style={[
            styles.handle,
            handleStyle,
            { position: 'absolute', left: 0, top: 0 }
          ]}
        />
      </PanGestureHandler>
    );
  };

  // Animated style for the container
  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
      width: itemWidth.value,
      height: itemHeight.value,
    };
  });

  return (
    <Animated.View
      style={[
        styles.draggableResizeableContainer,
        animatedStyle,
        isSelected && styles.selected,
      ]}
      onTouchStart={() => onSelect && onSelect()}
    >
      <PanGestureHandler onGestureEvent={panGestureHandler}>
        <Animated.View style={styles.dragArea}>
          <Image
            source={{ uri: source }}
            defaultSource={require('@/assets/images/placeholder-item.png')}
            style={styles.image}
            resizeMode="contain"
          />
        </Animated.View>
      </PanGestureHandler>

      {isSelected && (
        <>
          <ResizeHandle position="topLeft" />
          <ResizeHandle position="topRight" />
          <ResizeHandle position="bottomLeft" />
          <ResizeHandle position="bottomRight" />
        </>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  // Combined DraggableResizeableItem styles
  draggableResizeableContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dragArea: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  selected: {
    borderWidth: 2,
    borderColor: '#00ff00',
    borderStyle: 'dashed',
  },
  handle: {
    width: 20,
    height: 20,
    backgroundColor: 'rgba(0, 255, 0, 0.5)',
    borderRadius: 10,
    zIndex: 10,
  },
});