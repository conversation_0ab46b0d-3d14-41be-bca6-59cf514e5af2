import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
  Image,
  Dimensions,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import moment from 'moment';
import { getStylesDiary } from '@/methods/trips';
import { StyledDiaryItem, StyledDiaryText } from './styles';
import { PenIcon } from 'lucide-react-native';

import StyledDiaryModal from './style-diary-modal';
import { A } from '@expo/html-elements';

const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.5; // Aspect ratio of 2:3

export const StyledDiary = ({  eventId }: { eventId: string }) => {
  const [activeDay, setActiveDay] = useState<string | null>(moment().format('MM-DD-YYYY'));
  const { data: stylesDiary, refetch, isLoading, error, isRefetching } = getStylesDiary(eventId);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [imageLoadingStates, setImageLoadingStates] = useState<{[key: string]: boolean}>({});

  const styleDiaryList = stylesDiary?.data?.styleDiaries?.sort((a: any, b: any) => new Date(a?.date)?.getTime() - new Date(b?.date)?.getTime());

  const activeStyleDiary = styleDiaryList?.find((item: any) => item.date === activeDay);

  useEffect(() => {
    if (styleDiaryList?.length > 0) {
      setActiveDay(styleDiaryList[0].date);
    }
  }, [styleDiaryList]);

  const handleModalClose = () => {
    setIsModalVisible(false);
    refetch();
  };

  const handleImageLoadStart = (imageId: string) => {
    setImageLoadingStates(prev => ({ ...prev, [imageId]: true }));
  };

  const handleImageLoadEnd = (imageId: string) => {
    setTimeout(() => {
      setImageLoadingStates(prev => ({ ...prev, [imageId]: false }));
    }, 300);
  };

  const handleImageError = (imageId: string) => {
    setImageLoadingStates(prev => ({ ...prev, [imageId]: false }));
  };

  const DataItems = ({item}: {item: {date: string}}) => {
    const isActive = activeDay === item.date;

    return (
      <TouchableOpacity onPress={() => setActiveDay(item.date)}>
        <StyledDiaryItem style={{ borderBottomWidth: isActive ? 1 : 0, borderBottomColor: isActive ? '#0E7E61' : 'transparent' }}>
          <StyledDiaryText style={{ color: isActive ? '#0E7E61' : '#000000' }}>
            {moment(item.date, 'MM-DD-YYYY').format('ddd DD MMM YYYY')}
          </StyledDiaryText>
        </StyledDiaryItem>
      </TouchableOpacity>
    );
  }

  // Loading state for initial data fetch
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0E7E61" />
        <Text style={styles.loadingText}>Loading style diary...</Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load style diary</Text>
        <TouchableOpacity onPress={() => refetch()} style={styles.retryButton}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Empty state
  if (!styleDiaryList || styleDiaryList.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No style diary entries found</Text>
        <TouchableOpacity onPress={() => setIsModalVisible(true)} style={styles.addButton}>
          <PenIcon size={20} color="#0E7E61" />
          <Text style={styles.addButtonText}>Add your first entry</Text>
        </TouchableOpacity>
        <StyledDiaryModal
          activeStyleDiary={activeStyleDiary}
          isVisible={isModalVisible}
          onClose={handleModalClose}
          eventId={eventId}
        />
      </View>
    );
  }

  return (
    <View style={{ marginTop: 40}}>
      {/* Refetch Loading Overlay */}
      {isRefetching && (
        <View style={styles.refetchOverlay}>
          <View style={styles.refetchContainer}>
            <ActivityIndicator size="small" color="#0E7E61" />
            <Text style={styles.refetchText}>Updating...</Text>
          </View>
        </View>
      )}

      <FlatList
        contentContainerStyle={{gap: 10,  paddingBottom: 10, width: '100%'}}
        horizontal
        data={styleDiaryList || []}
        renderItem={({ item }) => <DataItems item={item} />}
        showsHorizontalScrollIndicator={false}
      />

      <View>
        <View style={{ width: '100%', borderRadius: 10, alignItems: 'flex-end', justifyContent: 'center',
          position: 'absolute',
          top: 30,
          right: 20,
          zIndex: 1000,
        }}>
          <TouchableOpacity onPress={() => setIsModalVisible(true)}>
            <PenIcon size={24} color="#0E7E61" />
          </TouchableOpacity>
        </View>

        <View style={{  width: '100%', borderRadius: 10, alignItems: 'flex-end', justifyContent: 'center',}}>
          {activeStyleDiary?.imageURL ? (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: activeStyleDiary.imageURL }}
                style={styles.mainImage}
                resizeMode='contain'
                onLoadStart={() => handleImageLoadStart(activeStyleDiary.date)}
                onLoadEnd={() => handleImageLoadEnd(activeStyleDiary.date)}
                onError={() => handleImageError(activeStyleDiary.date)}
              />

              {/* Image Loading Indicator */}
              {imageLoadingStates[activeStyleDiary.date] && (
                <View style={styles.imageLoadingOverlay}>
                  <ActivityIndicator size="large" color="#0E7E61" />
                  <Text style={styles.imageLoadingText}>Loading image...</Text>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.placeholderContainer}>
              <Text style={styles.placeholderText}>No image available</Text>
              <TouchableOpacity onPress={() => setIsModalVisible(true)} style={styles.addImageButton}>
                <PenIcon size={20} color="#0E7E61" />
                <Text style={styles.addImageButtonText}>Add image</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      <StyledDiaryModal
        activeStyleDiary={activeStyleDiary}
        isVisible={isModalVisible}
        onClose={handleModalClose}
        eventId={eventId}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 400,
    marginTop: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    fontFamily: 'MuktaVaani-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 400,
    marginTop: 40,
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'MuktaVaani-Regular',
  },
  retryButton: {
    backgroundColor: '#0E7E61',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'MuktaVaani-SemiBold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 400,
    marginTop: 40,
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'MuktaVaani-Regular',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F8F5',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0E7E61',
  },
  addButtonText: {
    color: '#0E7E61',
    fontSize: 16,
    fontFamily: 'MuktaVaani-SemiBold',
    marginLeft: 8,
  },
  refetchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'flex-start',
    alignItems: 'center',
    zIndex: 999,
    paddingTop: 20,
  },
  refetchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  refetchText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
    fontFamily: 'MuktaVaani-Regular',
  },
  imageContainer: {
    width: '100%',
    borderRadius: 10,
    position: 'relative',
  },
  mainImage: {
    width: '100%',
    height: 500,
    borderRadius: 10,
  },
  imageLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  imageLoadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#333',
    fontFamily: 'MuktaVaani-Regular',
  },
  placeholderContainer: {
    width: '100%',
    height: 500,
    borderRadius: 10,
    backgroundColor: '#F8F9FA',
    borderWidth: 2,
    borderColor: '#E9ECEF',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    fontFamily: 'MuktaVaani-Regular',
  },
  addImageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F8F5',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#0E7E61',
  },
  addImageButtonText: {
    color: '#0E7E61',
    fontSize: 14,
    fontFamily: 'MuktaVaani-SemiBold',
    marginLeft: 6,
  },
});