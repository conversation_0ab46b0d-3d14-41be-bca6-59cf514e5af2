import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
  Image,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';

import Button from '@/components/common/Button';

import { createStyleDiary } from '@/methods/style-diaries';
import { UploadImage, uploadImageToS3 } from '@/methods/cloths';

import { makeImageFromView } from '@shopify/react-native-skia';
import Tshirt from '@/assets/images/tshirt.png';
import Pants from '@/assets/images/pants.png';
import Shoes from '@/assets/images/shoes.png';
import { getPackingList } from '@/methods/trips';

import { Canvas } from './components/canvas';
import { DraggableResizeableItem } from './components/dragableItem';
import ZIndexControls from './components/zIndexControl';
import { STANDARD_CATEGORIES } from '@/utils/standardCategories';

const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.5; // Aspect ratio of 2:3

/**
 * Combined DraggableResizeableItem component that handles both dragging and resizing
 */

interface Item {
  id: string;
  name: string;
  source: string;
  type: string;
  category: string;
}

interface ItemSelectorProps {
  items: Item[];
  onSelectItem: (item: Item) => void;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

const ItemSelector: React.FC<ItemSelectorProps & { categories: string[] }> = ({
  items,
  onSelectItem,
  selectedCategory,
  onCategoryChange,
  categories
}) => {
  return (
    <View style={styles.itemSelectorContainer}>
      <View style={styles.categorySelector}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          style={styles.categoryScroll}
        >
          <TouchableOpacity
            style={[
              styles.categoryItem,
              selectedCategory === 'All' && styles.selectedCategory,
            ]}
            onPress={() => onCategoryChange('All')}
          >
            <Text style={[
              styles.categoryText,
              selectedCategory === 'All' && styles.selectedCategoryText,
            ]}>All</Text>
          </TouchableOpacity>
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryItem,
                selectedCategory === category && styles.selectedCategory,
              ]}
              onPress={() => onCategoryChange(category)}
            >
              <Text style={[
                styles.categoryText,
                selectedCategory === category && styles.selectedCategoryText,
              ]}>{category}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 10,
        }}
      >
        <Text style={styles.sectionTitle}>Add an Items</Text>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        style={styles.itemScroll}
      >

        {items
          .filter(item => selectedCategory === 'All' || item.category === selectedCategory)
          .map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.itemOption}
              onPress={() => onSelectItem(item)}
            >
              <Image
                source={{ uri: item.source }}
                style={styles.itemThumbnail}
                resizeMode="contain"
              />
              <View style={{ alignItems: 'flex-start', width: '100%' }}>
                <Text style={styles.itemLabel}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          ))}
      </ScrollView>
    </View>
  );
};

interface LoadingOverlayProps {
  visible: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ visible }) => {
  if (!visible) return null;

  return (
    <View style={styles.loadingOverlay}>
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Saving your style diary...</Text>
      </View>
    </View>
  );
};

interface CanvasItem {
  id: string;
  source: string;
  x: number;
  y: number;
  width: number;
  height: number;
  type: string;
  zIndex: number;
}

interface ItemChange {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
}

export const StyledDiary = ({
  eventId,
  activeStyleDiary,
  onClose,
}: {
  eventId: string;
  activeStyleDiary: any;
  onClose: () => void;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { data: packingList } = getPackingList(eventId);
  const [availableItems, setAvailableItems] = useState<Item[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [image, setImage] = useState('');
  const { mutate: createStyleDiaryMutation } = createStyleDiary();
  const {
    mutate: uploadImageMutation,
    isSuccess: isImageUploaded,
    data: imageData,
  } = UploadImage();
  const {
    mutate: uploadImageToS3Mutation,
    isSuccess: isImageUploadedToS3,
    data: imageDataToS3,
  } = uploadImageToS3();
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    if (packingList?.data?.packingList?.length > 0) {
      const Items: Item[] = [];
      const foundCategories = new Set<string>();
      packingList?.data?.packingList.forEach((category: any) => {
        if (category.type === 'category' && category.name) {
          foundCategories.add(category.name);
        }
        category.items.forEach((item: any) => {
          Items.push({
            category: category.name,
            id: item._id,
            name: item.name,
            source: item.imageUrl,
            type: item.type || 'Others', // Default to 'Others' if type is not specified
          });
        });
      });
      setAvailableItems(Items);
      setCategories(Array.from(foundCategories));
    }
  }, [packingList]);

  // State for items on the canvas
  const [canvasItems, setCanvasItems] = useState<CanvasItem[]>(activeStyleDiary?.items || []);
  // State for selected item
  const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);
  // Reference to the canvas for capturing as image
  const canvasRef = useRef<View>(null) as React.RefObject<View>;

  const captureView = async (canvas: React.RefObject<View>) => {
    if (!canvas.current) return;
    try {
      setIsLoading(true);
      setSelectedItemIndex(null);

      const image = await makeImageFromView(canvas);

      const base64Image = image?.encodeToBase64();

      if (!base64Image) {
        console.error('Failed to encode image to base64');
        setIsLoading(false);
        return;
      }

      setImage(base64Image);

      uploadImageMutation({
        fileName: `style-diary-${Date.now()}`,
        fileType: 'image/jpeg',
        folderPath: 'styleDiaries',
        imageUrl: base64Image,
      });
    } catch (error) {
      console.error('Error capturing view with Skia:', error);
      setIsLoading(false);
      throw error;
    }
  };

  // Handle successful pre-signed URL generation
  useEffect(() => {
    if (isImageUploaded && imageData?.preSignedURL) {
      uploadImageToS3Mutation(
        {
          imageUrl: image,
          preSignedUrl: imageData.preSignedURL,
        },
        {
          onSuccess: (response) => {
            console.log('success', response);
          },
          onError: (error) => {
            console.log('error', error);
            setIsLoading(false);
          },
        },
      );
    }
  }, [isImageUploaded, imageData]);

  // Handle successful S3 upload
  useEffect(() => {
    if (isImageUploadedToS3 && imageDataToS3 && imageData?.fileURL) {
      createStyleDiaryMutation(
        {
          styleDiaryId: activeStyleDiary._id,
          items: canvasItems,
          imageURL: imageData.fileURL,
        },
        {
          onSuccess: (response) => {
            console.log('success', response);
            setIsLoading(false);
            onClose();
          },
          onError: (error) => {
            console.log('error', error);
            setIsLoading(false);
          },
        },
      );
    }
  }, [isImageUploadedToS3, imageDataToS3, imageData]);

  const highestZIndex = canvasItems.reduce(
    (max: number, item: CanvasItem) => Math.max(max, item.zIndex || 1),
    0,
  );

  // Add item to canvas
  const handleAddItem = (item: Item) => {
    const newItem: CanvasItem = {
      id: Date.now().toString(),
      source: item.source,
      x: 50, // Initial position
      y: 50,
      width: 100, // Initial size
      height: 100,
      type: item.type,
      zIndex: highestZIndex + 1, // Place on top
    };

    setCanvasItems([...canvasItems, newItem]);
    // Select the newly added item
    setSelectedItemIndex(canvasItems.length);
  };

  // Update item position and size
  const handleItemChange = (index: number, changes: ItemChange) => {
    const updatedItems = [...canvasItems];
    updatedItems[index] = {
      ...updatedItems[index],
      x: changes.x !== undefined ? changes.x : updatedItems[index].x,
      y: changes.y !== undefined ? changes.y : updatedItems[index].y,
      width: changes.width !== undefined ? changes.width : updatedItems[index].width,
      height: changes.height !== undefined ? changes.height : updatedItems[index].height,
    };
    setCanvasItems(updatedItems);
  };

  // Delete selected item
  const handleDeleteItem = () => {
    if (selectedItemIndex !== null) {
      const updatedItems = canvasItems.filter(
        (_: CanvasItem, index: number) => index !== selectedItemIndex,
      );
      setCanvasItems(updatedItems);
      setSelectedItemIndex(null);
    }
  };

  const handleBringForward = () => {
    if (selectedItemIndex === null) return;

    const updatedItems = [...canvasItems];
    const currentItem = updatedItems[selectedItemIndex];

    // Find the item with the next highest z-index
    const itemsAbove = updatedItems.filter(
      (item, index) =>
        index !== selectedItemIndex && item.zIndex > currentItem.zIndex,
    );

    if (itemsAbove.length === 0) return; // Already at the top

    // Sort by z-index to find the next one up
    itemsAbove.sort((a, b) => a.zIndex - b.zIndex);
    const nextItemUp = itemsAbove[0];

    // Swap z-indices
    const tempZIndex = currentItem.zIndex;
    updatedItems[selectedItemIndex].zIndex = nextItemUp.zIndex;

    // Find the index of the next item up
    const nextItemIndex = updatedItems.findIndex(
      (item) => item.id === nextItemUp.id,
    );
    updatedItems[nextItemIndex].zIndex = tempZIndex;

    setCanvasItems(updatedItems);
  };

  // Send the selected item backward (decrease z-index)
  const handleSendBackward = () => {
    if (selectedItemIndex === null) return;

    const updatedItems = [...canvasItems];
    const currentItem = updatedItems[selectedItemIndex];

    // Find the item with the next lowest z-index
    const itemsBelow = updatedItems.filter(
      (item, index) =>
        index !== selectedItemIndex && item.zIndex < currentItem.zIndex,
    );

    if (itemsBelow.length === 0) return; // Already at the bottom

    // Sort by z-index in descending order to find the next one down
    itemsBelow.sort((a, b) => b.zIndex - a.zIndex);
    const nextItemDown = itemsBelow[0];

    // Swap z-indices
    const tempZIndex = currentItem.zIndex;
    updatedItems[selectedItemIndex].zIndex = nextItemDown.zIndex;

    // Find the index of the next item down
    const nextItemIndex = updatedItems.findIndex(
      (item) => item.id === nextItemDown.id,
    );
    updatedItems[nextItemIndex].zIndex = tempZIndex;

    setCanvasItems(updatedItems);
  };

  const sortedCanvasItems = [...canvasItems].sort(
    (a, b) => a.zIndex - b.zIndex,
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
      <LoadingOverlay visible={isLoading} />
      <ItemSelector
        items={availableItems}
        onSelectItem={handleAddItem}
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
        categories={categories}
      />
      <View ref={canvasRef} collapsable={false}>
        <Canvas showGrid={false}>
          {sortedCanvasItems.map((item, index) => (
            <DraggableResizeableItem
              key={item.id}
              source={item.source}
              initialX={item.x}
              initialY={item.y}
              initialWidth={item.width}
              initialHeight={item.height}
              isSelected={selectedItemIndex === index}
              onSelect={() => setSelectedItemIndex(index)}
              onPositionChange={(position) => handleItemChange(index, position)}
              onSizeChange={(changes) => handleItemChange(index, changes)}
            />
          ))}
        </Canvas>
      </View>
      <ZIndexControls
        onBringForward={handleBringForward}
        onSendBackward={handleSendBackward}
        onDelete={handleDeleteItem}
        disabled={selectedItemIndex === null}
      />
      <View style={{ height: 100 }}>
        <Button
          title="Save"
          onPress={() => {
            setSelectedItemIndex(null);
            Alert.alert(
              'Are you sure you want to save?',
              'This will save the current style diary and close the modal',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Save', onPress: () => captureView(canvasRef) },
              ],
              { cancelable: true },
            );
          }}
          isDisabled={isLoading}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  // App styles
  appContainer: {
    backgroundColor: '#F0F0F0',
  },

  // StyledDiary styles
  container: {
    marginTop: 20,
    backgroundColor: '#F0F0F0',
  },
  header: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#000',
  },
  tabText: {
    fontSize: 16,
    color: '#888',
  },
  activeTabText: {
    color: '#000',
    fontWeight: 'bold',
  },

  // Canvas styles
  canvasContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  canvas: {
    width: CANVAS_WIDTH,
    height: CANVAS_HEIGHT - 100,
    backgroundColor: '#f5f5f5',
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 0, 0.5)', // Yellow with opacity
  },
  verticalCenter: {
    width: 1,
    height: '100%',
    left: '50%',
  },
  verticalLeft: {
    width: 1,
    height: '100%',
    left: '33%',
    borderStyle: 'dashed',
  },
  verticalRight: {
    width: 1,
    height: '100%',
    left: '67%',
    borderStyle: 'dashed',
  },
  horizontalCenter: {
    width: '100%',
    height: 1,
    top: '50%',
  },
  horizontalTop: {
    width: '100%',
    height: 1,
    top: '33%',
    borderStyle: 'dashed',
  },
  horizontalBottom: {
    width: '100%',
    height: 1,
    top: '67%',
    borderStyle: 'dashed',
  },

  // Combined DraggableResizeableItem styles
  draggableResizeableContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dragArea: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  selected: {
    borderWidth: 2,
    borderColor: '#00ff00',
    borderStyle: 'dashed',
  },
  handle: {
    width: 20,
    height: 20,
    backgroundColor: 'rgba(0, 255, 0, 0.5)',
    borderRadius: 10,
    zIndex: 10,
  },

  // Action bar styles
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 18,
  },
  saveButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#ccff00',
    borderRadius: 20,
  },
  saveButtonText: {
    color: '#000',
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },

  // ItemSelector styles
  itemSelectorContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
    textAlign: 'center',
  },
  itemScroll: {
    flexDirection: 'row',
  },
  itemOption: {
    marginRight: 15,
    alignItems: 'center',
  },
  itemThumbnail: {
    width: 100,
    height: 100,
    backgroundColor: '#fff',
    borderRadius: 5,
  },
  itemLabel: {
    color: '#000',
    marginTop: 5,
    fontSize: 12,
  },

  // Bottom tabs styles
  bottomTabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  bottomTab: {
    alignItems: 'center',
  },
  bottomTabText: {
    fontSize: 24,
  },
  activeBottomTab: {
    transform: [{ scale: 1.2 }],
  },
  activeBottomTabText: {
    color: '#000',
  },
  bottomTabLabel: {
    fontSize: 12,
    marginTop: 5,
  },

  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
  categorySelector: {
    marginBottom: 15,
  },
  categoryScroll: {
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  categoryItem: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedCategory: {
    backgroundColor: '#000',
    borderColor: '#000',
  },
  categoryText: {
    fontSize: 14,
    color: '#333',
  },
  selectedCategoryText: {
    color: '#fff',
  },
});
