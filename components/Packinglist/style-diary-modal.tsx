import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';
import { StyledDiary } from './style-diary-add-modal';
import {
  TemplateModalContainer,
  TemplateModalHeader,
  TemplateModalTitle,
  HeaderText,
} from '../TemplateListModal/styles';

interface StyledDiaryModalProps {
  isVisible: boolean;
  onClose: () => void;
  eventId: string;
  activeStyleDiary: any;
}

export default function StyledDiaryModal({ isVisible, onClose, eventId, activeStyleDiary }: StyledDiaryModalProps) {

  console.log('activeStyleDiary', activeStyleDiary);
  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <TemplateModalContainer style={{ height: '90%'}}>
        <TemplateModalHeader>
          <TouchableOpacity onPress={onClose}>
            <HeaderText>Close</HeaderText>
          </TouchableOpacity>
          <TemplateModalTitle>Style Diary</TemplateModalTitle>
          <View style={{ width: 50 }} />
        </TemplateModalHeader>
        <StyledDiary eventId={eventId} activeStyleDiary={activeStyleDiary} onClose={onClose}/>
      </TemplateModalContainer>
    </Modal>
  );
} 