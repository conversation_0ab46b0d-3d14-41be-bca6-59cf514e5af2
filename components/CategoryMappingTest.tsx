import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { createCategoryIdToNameMapping, mapCategoryIdToName, clearCategoryCache } from '@/utils/categoryMapping';
import { createMergedCategories, getStandardCategoryName, clearMergedCategoriesCache } from '@/utils/categoryMerger';
import { getClothes } from '@/methods/cloths';

interface CategoryMappingTestProps {
  visible?: boolean;
}

export default function CategoryMappingTest({ visible = false }: CategoryMappingTestProps) {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runQuickTest = async () => {
    setIsLoading(true);
    setTestResults(['Running quick test...']);

    try {
      const mapping = await createCategoryIdToNameMapping();
      const success = mapping.size > 0;
      setTestResults(prev => [...prev, `Quick test ${success ? 'PASSED' : 'FAILED'} - ${mapping.size} categories`]);
    } catch (error) {
      setTestResults(prev => [...prev, `Quick test ERROR: ${error}`]);
    }

    setIsLoading(false);
  };

  const testMergedCategories = async () => {
    setIsLoading(true);
    setTestResults(['Testing merged categories system...']);

    try {
      // Mock some items with the actual IDs from your logs
      const mockItems = [
        { _id: '1', name: 'phone', itemCategoryId: 'ZnPdEhJKbF78onLRd' },
        { _id: '2', name: 'test3', itemCategoryId: 'oGCjqfavcm2QHfTe4' },
        { _id: '3', name: 'Test002', itemCategoryId: 'basic-dresses' },
        { _id: '4', name: 'Test003', itemCategoryId: 'basic-shoes' },
        { _id: '5', name: 'Jumpsuits', itemCategoryId: 'qSGuakj5eNzwBMKf3' }
      ];

      setTestResults(prev => [...prev, `Testing with ${mockItems.length} mock items`]);

      // Test merged categories creation
      const mergedCategories = await createMergedCategories(mockItems);
      setTestResults(prev => [...prev, `Created ${mergedCategories.length} merged categories`]);

      // Test each mock item
      for (const item of mockItems) {
        const categoryName = await getStandardCategoryName(item.itemCategoryId, mockItems);
        setTestResults(prev => [...prev, `${item.name} (${item.itemCategoryId}) → ${categoryName}`]);
      }

      setTestResults(prev => [...prev, 'Merged categories test completed']);
    } catch (error) {
      setTestResults(prev => [...prev, `Merged categories test ERROR: ${error}`]);
    }

    setIsLoading(false);
  };

  const runFullTest = async () => {
    setIsLoading(true);
    setTestResults(['Running full test...']);
    
    try {
      // Capture console logs
      const originalLog = console.log;
      const logs: string[] = [];
      
      console.log = (...args) => {
        logs.push(args.join(' '));
        originalLog(...args);
      };

      // await testCategoryMapping();
      
      // Restore console.log
      console.log = originalLog;
      
      setTestResults(logs);
    } catch (error) {
      setTestResults(prev => [...prev, `Full test ERROR: ${error}`]);
    }
    
    setIsLoading(false);
  };

  const testSpecificIds = async () => {
    setIsLoading(true);
    setTestResults(['Testing specific category IDs...']);

    try {
      // Test with the actual IDs from your logs
      const testIds = [
        'ZnPdEhJKbF78onLRd', // from your logs
        'oGCjqfavcm2QHfTe4', // from your logs
        'TXnwWN3k83q3JE3Lm', // from your logs
        'ZbPPByTRT8jsKNTAK', // from your logs
        'qSGuakj5eNzwBMKf3', // from your logs (Jumpsuits)
        'basic-dresses',      // from your logs
        'basic-shoes',        // from your logs
        'tops', 'bottoms', 'shoes' // fallback tests
      ];

      const mapping = await createCategoryIdToNameMapping();

      setTestResults(prev => [...prev, `Created mapping with ${mapping.size} categories`]);

      for (const id of testIds) {
        const name = await mapCategoryIdToName(id);
        setTestResults(prev => [...prev, `${id} → ${name}`]);
      }

      setTestResults(prev => [...prev, 'Specific ID test completed']);
    } catch (error) {
      setTestResults(prev => [...prev, `Specific ID test ERROR: ${error}`]);
    }

    setIsLoading(false);
  };

  const clearCacheAndTest = async () => {
    setIsLoading(true);
    setTestResults(['Clearing cache and testing...']);

    try {
      clearCategoryCache();
      setTestResults(prev => [...prev, 'Cache cleared']);

      // Test with actual IDs
      await testSpecificIds();
    } catch (error) {
      setTestResults(prev => [...prev, `Clear cache test ERROR: ${error}`]);
    }

    setIsLoading(false);
  };

  if (!visible) {
    return null;
  }

  return (
    <View style={{
      position: 'absolute',
      top: 100,
      left: 20,
      right: 20,
      backgroundColor: 'white',
      padding: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: '#0E7E61',
      zIndex: 1000,
      maxHeight: 400
    }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
        Category Mapping Test
      </Text>
      
      <View style={{ flexDirection: 'row', marginBottom: 10, flexWrap: 'wrap' }}>
        <TouchableOpacity
          onPress={runQuickTest}
          disabled={isLoading}
          style={{
            backgroundColor: '#0E7E61',
            padding: 8,
            borderRadius: 5,
            marginRight: 5,
            marginBottom: 5,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>Quick</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={testMergedCategories}
          disabled={isLoading}
          style={{
            backgroundColor: '#2E8B57',
            padding: 8,
            borderRadius: 5,
            marginRight: 5,
            marginBottom: 5,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>Merged</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={runFullTest}
          disabled={isLoading}
          style={{
            backgroundColor: '#0E7E61',
            padding: 8,
            borderRadius: 5,
            marginRight: 5,
            marginBottom: 5,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>Full</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testSpecificIds}
          disabled={isLoading}
          style={{
            backgroundColor: '#0E7E61',
            padding: 10,
            borderRadius: 5,
            marginRight: 10,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white' }}>Test IDs</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={clearCacheAndTest}
          disabled={isLoading}
          style={{
            backgroundColor: '#E74C3C',
            padding: 10,
            borderRadius: 5,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white' }}>Clear & Test</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={{ maxHeight: 250 }}>
        {testResults.map((result, index) => (
          <Text key={index} style={{ fontSize: 12, marginBottom: 2 }}>
            {result}
          </Text>
        ))}
      </ScrollView>
      
      {isLoading && (
        <Text style={{ textAlign: 'center', marginTop: 10 }}>
          Testing...
        </Text>
      )}
    </View>
  );
}
