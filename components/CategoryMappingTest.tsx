import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { createCategoryIdToNameMapping, mapCategoryIdToName } from '@/utils/categoryMapping';

interface CategoryMappingTestProps {
  visible?: boolean;
}

export default function CategoryMappingTest({ visible = false }: CategoryMappingTestProps) {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runQuickTest = async () => {
    setIsLoading(true);
    setTestResults(['Running quick test...']);
    
    try {
      const success = await quickCategoryTest();
      setTestResults(prev => [...prev, `Quick test ${success ? 'PASSED' : 'FAILED'}`]);
    } catch (error) {
      setTestResults(prev => [...prev, `Quick test ERROR: ${error}`]);
    }
    
    setIsLoading(false);
  };

  const runFullTest = async () => {
    setIsLoading(true);
    setTestResults(['Running full test...']);
    
    try {
      // Capture console logs
      const originalLog = console.log;
      const logs: string[] = [];
      
      console.log = (...args) => {
        logs.push(args.join(' '));
        originalLog(...args);
      };

      // await testCategoryMapping();
      
      // Restore console.log
      console.log = originalLog;
      
      setTestResults(logs);
    } catch (error) {
      setTestResults(prev => [...prev, `Full test ERROR: ${error}`]);
    }
    
    setIsLoading(false);
  };

  const testSpecificIds = async () => {
    setIsLoading(true);
    setTestResults(['Testing specific category IDs...']);
    
    try {
      const testIds = ['tops', 'bottoms', 'shoes', 'dresses', 'accessories'];
      const mapping = await createCategoryIdToNameMapping();
      
      setTestResults(prev => [...prev, `Created mapping with ${mapping.size} categories`]);
      
      for (const id of testIds) {
        const name = await mapCategoryIdToName(id);
        setTestResults(prev => [...prev, `${id} → ${name}`]);
      }
      
      setTestResults(prev => [...prev, 'Specific ID test completed']);
    } catch (error) {
      setTestResults(prev => [...prev, `Specific ID test ERROR: ${error}`]);
    }
    
    setIsLoading(false);
  };

  if (!visible) {
    return null;
  }

  return (
    <View style={{
      position: 'absolute',
      top: 100,
      left: 20,
      right: 20,
      backgroundColor: 'white',
      padding: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: '#0E7E61',
      zIndex: 1000,
      maxHeight: 400
    }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
        Category Mapping Test
      </Text>
      
      <View style={{ flexDirection: 'row', marginBottom: 10 }}>
        <TouchableOpacity
          onPress={runQuickTest}
          disabled={isLoading}
          style={{
            backgroundColor: '#0E7E61',
            padding: 10,
            borderRadius: 5,
            marginRight: 10,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white' }}>Quick Test</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={runFullTest}
          disabled={isLoading}
          style={{
            backgroundColor: '#0E7E61',
            padding: 10,
            borderRadius: 5,
            marginRight: 10,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white' }}>Full Test</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testSpecificIds}
          disabled={isLoading}
          style={{
            backgroundColor: '#0E7E61',
            padding: 10,
            borderRadius: 5,
            opacity: isLoading ? 0.5 : 1
          }}
        >
          <Text style={{ color: 'white' }}>Test IDs</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={{ maxHeight: 250 }}>
        {testResults.map((result, index) => (
          <Text key={index} style={{ fontSize: 12, marginBottom: 2 }}>
            {result}
          </Text>
        ))}
      </ScrollView>
      
      {isLoading && (
        <Text style={{ textAlign: 'center', marginTop: 10 }}>
          Testing...
        </Text>
      )}
    </View>
  );
}
