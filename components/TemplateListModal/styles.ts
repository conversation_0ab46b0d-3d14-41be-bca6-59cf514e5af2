import { View, Text, TouchableOpacity, Image } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';

export const TemplateModalContainer = styled(View)`
  height: 80%;
  background-color: #F0F0F0;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px;
`;

export const TemplateModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const HeaderText = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaani;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;

export const TemplateModalTitle = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaaniSemiBold;
`;

export const TemplateItemContainer = styled(View) <{ width: number }>`
  flex: 1;
  width: ${({ width }: { width: number }) => width - 10}px;
`;

export const TemplateItemName = styled(Text)`
  font-size: 15px;
  font-family: MuktaVaaniSemiBold;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  margin-bottom: 5px;
`;

export const TemplateItemDescription = styled(Text)`
  font-size: 14px;
  font-family: MuktaVaani;
  color: #000000;
`;

export const TemplatesContainer = styled(View)`
  flex: 1;
`;

export const CategoryItem = styled(TouchableOpacity) <{ isSelected: boolean }>`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 10px;
  margin: 5px;
  padding-horizontal: 16px;
  padding-vertical: 8px;
  background-color: ${({ isSelected, theme }: { isSelected: boolean; theme: DefaultTheme }) =>
    isSelected ? theme.brand.green[500] : '#F5F5F5'};
`;

export const CategoryItemText = styled(Text) <{ isSelected: boolean }>`
  font-size: 16px;
  font-family: MuktaVaani;
  color: ${({ isSelected }: { isSelected: boolean }) => (isSelected ? '#FFF' : '#000')};
`;

export const TemplateImage = styled(Image)`
  width: 100%;
  height: 120px;
  border-radius: 10px;
  background-color: gray;
`;

export const TemplatePreviewContainer = styled(View)`
  flex: 1;
  margin-top: 20px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 12px;
`;

export const TemplatePreviewHeader = styled(View)`

`;

export const TemplatePreviewTitle = styled(Text)`
  font-size: 24px;
  font-family: MuktaVaaniSemiBold;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;

export const TemplatePreviewDescription = styled(Text)`
  font-size: 16px;
  font-family: MuktaVaani;
  color: #666666;
  line-height: 22px;
`;

export const TemplateItemsList = styled(View)`

`;

export const TemplateItemRow = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  padding: 12px 0;
  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE;
  background-color: #FFFFFF;
`;

export const TemplateItemCheckbox = styled(TouchableOpacity) <{ isChecked: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 6px;
  border-width: 2px;
  border-color: ${({ isChecked, theme }) =>
    isChecked ? theme.brand.green[500] : '#CCCCCC'};
  background-color: ${({ isChecked, theme }) =>
    isChecked ? theme.brand.green[500] : '#FFFFFF'};
  margin-right: 12px;
  justify-content: center;
  align-items: center;
  elevation: ${({ isChecked }) => (isChecked ? 2 : 0)};
  shadow-color: #000;
  shadow-offset: ${({ isChecked }) => (isChecked ? '0px 1px' : '0px 0px')};
  shadow-opacity: ${({ isChecked }) => (isChecked ? 0.2 : 0)};
  shadow-radius: ${({ isChecked }) => (isChecked ? 2 : 0)};
`;

export const TemplatePreviewItemName = styled(Text)`
  font-size: 16px;
  font-family: MuktaVaani;
  color: #333333;
`;

export const TemplateItemQuantity = styled(Text)`
  font-size: 14px;
  font-family: MuktaVaani;
  color: #666666;
  margin-left: 8px;
`;