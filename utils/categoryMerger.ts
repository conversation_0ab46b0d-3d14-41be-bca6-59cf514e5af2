import { CategoryItem, getCurrentUserGender } from '@/data/categories';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import Meteor from '@meteorrn/core';

// Cache for merged categories
let mergedCategoriesCache: MergedCategory[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export interface MergedCategory {
  id: string;           // Standard category ID (e.g., "tops", "bottoms")
  name: string;         // Display name (e.g., "Tops", "Bottoms")
  realIds: string[];    // Array of actual database ObjectIds that map to this category
  hasItems: boolean;    // Whether this category has actual items
}

/**
 * Creates a merged category system that maps real database IDs to standard category names
 */
export async function createMergedCategories(items: any[] = []): Promise<MergedCategory[]> {
  // Check cache - temporarily disabled for testing
  const now = Date.now();
  // if (mergedCategoriesCache && (now - cacheTimestamp) < CACHE_DURATION) {
  //   console.log('Using cached merged categories');
  //   return updateItemCounts(mergedCategoriesCache, items);
  // }

  console.log('🔄 Creating merged category system...');

  try {
    // Step 1: Get all real categories from database
    const realCategories = await fetchAllRealCategories();
    console.log(`📋 Found ${realCategories.length} real categories from database`);

    // Step 2: Create standard category templates
    const standardCategories = createStandardCategoryTemplates();
    console.log(`📋 Created ${standardCategories.length} standard category templates`);

    // Step 3: Map real categories to standard categories
    const mergedCategories = mapRealToStandardCategories(realCategories, standardCategories);
    console.log(`✅ Created ${mergedCategories.length} merged categories`);

    // Step 4: Update with item counts
    const categoriesWithItems = updateItemCounts(mergedCategories, items);

    // Cache the result
    mergedCategoriesCache = categoriesWithItems;
    cacheTimestamp = now;

    return categoriesWithItems;

  } catch (error) {
    console.error('❌ Error creating merged categories:', error);
    return createFallbackCategories();
  }
}

/**
 * Fetches all real categories from the database
 */
async function fetchAllRealCategories(): Promise<any[]> {
  try {
    const result = await new Promise<any>((resolve, reject) => {
      Meteor.call('itemCategories-fetch', {}, (err: any, res: any) => {
        if (err) {
          console.error('Error fetching real categories:', err);
          reject(err);
          return;
        }
        resolve(res);
      });
    });

    return result?.data?.itemCategories || [];
  } catch (error) {
    console.error('Error in fetchAllRealCategories:', error);
    return [];
  }
}

/**
 * Creates standard category templates
 */
function createStandardCategoryTemplates(): MergedCategory[] {
  const standardNames = [
    'Tops', 'Bottoms', 'Dresses', 'Shoes', 'Accessories', 
    'Outerwear', 'Bags', 'Loungewear', 'Activewear', 
    'Swimwear', 'Underwear', 'Sleepwear', 'Jewelry', 'Tech', 'Others'
  ];

  return standardNames.map(name => ({
    id: name.toLowerCase().replace(/\s+/g, '-'),
    name,
    realIds: [],
    hasItems: false
  }));
}

/**
 * Maps real database categories to standard categories based on name similarity
 */
function mapRealToStandardCategories(
  realCategories: any[], 
  standardCategories: MergedCategory[]
): MergedCategory[] {
  
  console.log('🔄 Mapping real categories to standard categories...');

  // Create a copy of standard categories to work with
  const mergedCategories = standardCategories.map(cat => ({ ...cat, realIds: [] }));

  // Process each real category
  realCategories.forEach(realCategory => {
    if (!realCategory._id) return;

    const categoryName = realCategory.name || realCategory.mainCategory || '';
    const categoryId = realCategory._id;

    console.log(`📝 Processing real category: ${categoryId} → "${categoryName}"`);

    // Find the best matching standard category
    const matchedStandardCategory = findBestMatch(categoryName, mergedCategories);
    
    if (matchedStandardCategory) {
      matchedStandardCategory.realIds.push(categoryId);
      console.log(`✅ Mapped ${categoryId} to standard category "${matchedStandardCategory.name}"`);
    } else {
      // If no match found, add to "Others"
      const othersCategory = mergedCategories.find(cat => cat.id === 'others');
      if (othersCategory) {
        othersCategory.realIds.push(categoryId);
        console.log(`⚠️ Mapped ${categoryId} to "Others" category`);
      }
    }
  });

  return mergedCategories;
}

/**
 * Finds the best matching standard category for a real category name
 */
function findBestMatch(categoryName: string, standardCategories: MergedCategory[]): MergedCategory | null {
  if (!categoryName) return null;

  const lowerCategoryName = categoryName.toLowerCase().trim();

  // Define mapping rules
  const mappingRules: { [key: string]: string[] } = {
    'tops': ['top', 'shirt', 'blouse', 'sweater', 'hoodie', 'jacket', 'coat', 'cardigan', 'tank', 'tee'],
    'bottoms': ['bottom', 'pant', 'jean', 'trouser', 'short', 'skirt', 'legging'],
    'dresses': ['dress', 'gown', 'frock', 'jumpsuit'],
    'shoes': ['shoe', 'boot', 'sneaker', 'sandal', 'heel', 'flat', 'loafer'],
    'accessories': ['accessory', 'belt', 'hat', 'cap', 'scarf', 'glove', 'watch'],
    'outerwear': ['coat', 'jacket', 'blazer', 'vest', 'parka'],
    'bags': ['bag', 'purse', 'backpack', 'tote', 'clutch', 'wallet'],
    'loungewear': ['lounge', 'pajama', 'nightwear', 'robe'],
    'activewear': ['active', 'sport', 'gym', 'workout', 'athletic'],
    'swimwear': ['swim', 'bikini', 'swimsuit', 'bathing'],
    'underwear': ['underwear', 'bra', 'panty', 'brief', 'boxer'],
    'sleepwear': ['sleep', 'pajama', 'nightgown', 'nightie'],
    'jewelry': ['jewelry', 'necklace', 'ring', 'earring', 'bracelet'],
    'tech': ['tech', 'phone', 'laptop', 'tablet', 'electronic', 'computer', 'device', 'gadget', 'lenovo', 'apple', 'samsung', 'iphone', 'android']
  };

  // Try exact match first
  for (const standardCategory of standardCategories) {
    if (standardCategory.name.toLowerCase() === lowerCategoryName) {
      return standardCategory;
    }
  }

  // Try keyword matching
  for (const [standardId, keywords] of Object.entries(mappingRules)) {
    for (const keyword of keywords) {
      if (lowerCategoryName.includes(keyword) || keyword.includes(lowerCategoryName)) {
        const matchedCategory = standardCategories.find(cat => cat.id === standardId);
        if (matchedCategory) {
          return matchedCategory;
        }
      }
    }
  }

  return null;
}

/**
 * Updates the hasItems flag based on actual items
 */
function updateItemCounts(categories: MergedCategory[], items: any[]): MergedCategory[] {
  console.log('🔄 Updating item counts for categories...');

  const updatedCategories = categories.map(category => {
    const matchingItems: any[] = [];

    const hasItems = items.some(item => {
      // Method 1: Check if item's categoryId matches any of this category's realIds
      if (item.itemCategoryId && category.realIds.includes(item.itemCategoryId)) {
        matchingItems.push(item);
        return true;
      }

      // Method 2: Check for basic- and standard- prefixed IDs
      if (item.itemCategoryId) {
        const cleanId = item.itemCategoryId.replace(/^(basic-|standard-)/, '');
        if (cleanId.toLowerCase() === category.id) {
          matchingItems.push(item);
          return true;
        }
      }

      // Method 3: Fallback - check item name against category keywords
      if (item.name && shouldItemBeInCategory(item.name, category.id)) {
        matchingItems.push(item);
        console.log(`📝 Item "${item.name}" mapped to "${category.name}" by name analysis`);
        return true;
      }

      return false;
    });

    if (matchingItems.length > 0) {
      console.log(`✅ Category "${category.name}" has ${matchingItems.length} items:`, matchingItems.map(i => i.name));
    } else {
      console.log(`❌ Category "${category.name}" has no items`);
    }

    return {
      ...category,
      hasItems
    };
  });

  return updatedCategories;
}

/**
 * Determines if an item should be in a specific category based on its name
 */
function shouldItemBeInCategory(itemName: string, categoryId: string): boolean {
  if (!itemName) return false;

  const lowerItemName = itemName.toLowerCase().trim();

  // Define mapping rules for item names
  const itemNameRules: { [key: string]: string[] } = {
    'tech': ['phone', 'laptop', 'tablet', 'computer', 'device', 'gadget', 'lenovo', 'apple', 'samsung', 'iphone', 'android', 'ipad', 'macbook', 'dell', 'hp', 'asus'],
    'tops': ['shirt', 'blouse', 'sweater', 'hoodie', 'jacket', 'coat', 'cardigan', 'tank', 'tee', 'top'],
    'bottoms': ['pant', 'jean', 'trouser', 'short', 'skirt', 'legging', 'bottom'],
    'dresses': ['dress', 'gown', 'frock', 'jumpsuit'],
    'shoes': ['shoe', 'boot', 'sneaker', 'sandal', 'heel', 'flat', 'loafer'],
    'accessories': ['belt', 'hat', 'cap', 'scarf', 'glove', 'watch', 'accessory'],
    'bags': ['bag', 'purse', 'backpack', 'tote', 'clutch', 'wallet']
  };

  const keywords = itemNameRules[categoryId] || [];

  return keywords.some(keyword =>
    lowerItemName.includes(keyword) || keyword.includes(lowerItemName)
  );
}

/**
 * Creates fallback categories when the main system fails
 */
function createFallbackCategories(): MergedCategory[] {
  return [
    { id: 'tops', name: 'Tops', realIds: [], hasItems: false },
    { id: 'bottoms', name: 'Bottoms', realIds: [], hasItems: false },
    { id: 'dresses', name: 'Dresses', realIds: [], hasItems: false },
    { id: 'shoes', name: 'Shoes', realIds: [], hasItems: false },
    { id: 'accessories', name: 'Accessories', realIds: [], hasItems: false }
  ];
}

/**
 * Gets categories that have items for display in UI
 */
export async function getCategoriesWithItems(items: any[] = []): Promise<MergedCategory[]> {
  const allCategories = await createMergedCategories(items);
  return allCategories.filter(category => category.hasItems);
}

/**
 * Maps an item's categoryId to a standard category name
 */
export async function getStandardCategoryName(itemCategoryId: string, items: any[] = []): Promise<string> {
  if (!itemCategoryId) return 'Uncategorized';

  const mergedCategories = await createMergedCategories(items);
  
  for (const category of mergedCategories) {
    if (category.realIds.includes(itemCategoryId)) {
      return category.name;
    }
  }

  // Check for basic- and standard- prefixed IDs
  const cleanId = itemCategoryId.replace(/^(basic-|standard-)/, '');
  const matchedCategory = mergedCategories.find(cat => cat.id === cleanId.toLowerCase());
  
  return matchedCategory ? matchedCategory.name : 'Others';
}

/**
 * Clears the merged categories cache
 */
export function clearMergedCategoriesCache(): void {
  mergedCategoriesCache = null;
  cacheTimestamp = 0;
  console.log('Merged categories cache cleared');
}
