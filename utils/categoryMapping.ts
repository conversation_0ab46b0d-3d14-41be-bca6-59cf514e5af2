import { CategoryItem, getCurrentUserGender } from '@/data/categories';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import Meteor from '@meteorrn/core';

// Cache for category mappings to avoid repeated API calls
let categoryCache: Map<string, string> | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Creates a mapping from category IDs to category names
 * Uses the gender-based categories from the backend API
 */
export async function createCategoryIdToNameMapping(): Promise<Map<string, string>> {
  // Check if we have a valid cache
  const now = Date.now();
  if (categoryCache && (now - cacheTimestamp) < CACHE_DURATION) {
    console.log('Using cached category mapping');
    return categoryCache;
  }

  console.log('Creating fresh category ID to name mapping...');
  
  const mapping = new Map<string, string>();
  
  try {
    // Get user's gender
    let gender = getCurrentUserGender();
    
    // If gender is not available from current user, try to fetch from server
    if (!gender && Meteor.userId()) {
      try {
        const userProfile = await new Promise<any>((resolve, reject) => {
          Meteor.call('users-getUser', (err: any, res: any) => {
            if (err) {
              console.error('Error fetching user data:', err);
              reject(err);
              return;
            }
            resolve(res);
          });
        });

        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
        }
      } catch (error) {
        console.error('Error fetching user gender:', error);
      }
    }

    // Fetch categories from backend
    if (gender) {
      console.log('Fetching categories for gender:', gender);
      const categories = await fetchCategoriesFromBackend(gender);
      
      // Create mapping from category IDs to names
      categories.forEach(category => {
        if (category.id && category.name) {
          mapping.set(category.id, category.name);
          
          // Also add children if they exist
          if (category.children) {
            category.children.forEach(child => {
              if (child.id && child.name) {
                mapping.set(child.id, child.name);
              }
            });
          }
        }
      });
      
      console.log(`Created mapping for ${mapping.size} categories`);
    }

    // Add fallback mappings for common categories
    const fallbackMappings = {
      'tops': 'Tops',
      'bottoms': 'Bottoms',
      'shoes': 'Shoes',
      'dresses': 'Dresses',
      'accessories': 'Accessories',
      'outerwear': 'Outerwear',
      'loungewear': 'Loungewear',
      'activewear': 'Activewear',
      'swimwear': 'Swimwear',
      'underwear': 'Underwear',
      'sleepwear': 'Sleepwear',
      'jewelry': 'Jewelry',
      'bags': 'Bags',
      'tech': 'Tech',
      'others': 'Others'
    };

    // Add fallback mappings if they don't already exist
    Object.entries(fallbackMappings).forEach(([id, name]) => {
      if (!mapping.has(id)) {
        mapping.set(id, name);
      }
    });

  } catch (error) {
    console.error('Error creating category mapping:', error);
    
    // Use fallback mappings only
    const fallbackMappings = {
      'tops': 'Tops',
      'bottoms': 'Bottoms',
      'shoes': 'Shoes',
      'dresses': 'Dresses',
      'accessories': 'Accessories',
      'outerwear': 'Outerwear',
      'loungewear': 'Loungewear',
      'activewear': 'Activewear',
      'swimwear': 'Swimwear',
      'underwear': 'Underwear',
      'sleepwear': 'Sleepwear',
      'jewelry': 'Jewelry',
      'bags': 'Bags',
      'tech': 'Tech',
      'others': 'Others'
    };

    Object.entries(fallbackMappings).forEach(([id, name]) => {
      mapping.set(id, name);
    });
  }

  // Cache the mapping
  categoryCache = mapping;
  cacheTimestamp = now;
  
  return mapping;
}

/**
 * Maps a category ID to its display name
 * Uses cached mapping for performance
 */
export async function mapCategoryIdToName(categoryId: string): Promise<string> {
  if (!categoryId) {
    return 'Uncategorized';
  }

  const mapping = await createCategoryIdToNameMapping();
  
  // Try exact match first
  if (mapping.has(categoryId)) {
    return mapping.get(categoryId)!;
  }

  // Try case-insensitive match
  const lowerCategoryId = categoryId.toLowerCase();
  const entries = Array.from(mapping.entries());

  for (const [id, name] of entries) {
    if (id.toLowerCase() === lowerCategoryId) {
      return name;
    }
  }

  // Try partial matches
  for (const [id, name] of entries) {
    if (lowerCategoryId.includes(id.toLowerCase()) || id.toLowerCase().includes(lowerCategoryId)) {
      return name;
    }
  }

  // If no match found, return a formatted version of the ID
  return categoryId.charAt(0).toUpperCase() + categoryId.slice(1).toLowerCase();
}

/**
 * Enhanced category display name function that handles multiple data sources
 */
export async function getCategoryDisplayName(item: any): Promise<string> {
  console.log('🔍 getCategoryDisplayName called with item:', {
    name: item.name,
    category: item.category,
    itemCategoryId: item.itemCategoryId
  });

  // First try to get the category name from the item.category object (from aggregation)
  if (item.category?.name) {
    console.log('✅ Using item.category.name:', item.category.name);
    return item.category.name;
  }

  // If we have an itemCategoryId, map it using our backend categories
  if (item.itemCategoryId) {
    console.log('🔄 Mapping itemCategoryId:', item.itemCategoryId);
    const mappedName = await mapCategoryIdToName(item.itemCategoryId);
    console.log('✅ Mapped to:', mappedName);
    return mappedName;
  }

  // Fallback to 'Uncategorized'
  console.log('⚠️ No category info available, using Uncategorized');
  return 'Uncategorized';
}

/**
 * Clears the category cache (useful when user changes gender or categories are updated)
 */
export function clearCategoryCache(): void {
  categoryCache = null;
  cacheTimestamp = 0;
  console.log('Category cache cleared');
}
