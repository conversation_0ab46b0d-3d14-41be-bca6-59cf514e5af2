import Meteor from '@meteorrn/core';
import { getCurrentUserGender } from '@/data/categories';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';

/**
 * Debug function to check what's happening with category data
 * Call this from the console or add it to a component
 */
export async function debugCategoryIssue() {
  console.log('🔍 DEBUGGING CATEGORY ISSUE 🔍\n');

  try {
    // Step 1: Check user gender
    console.log('1. Checking user gender...');
    const gender = getCurrentUserGender();
    console.log('Current user gender:', gender);

    // Step 2: Fetch categories from backend
    console.log('\n2. Fetching categories from backend...');
    let backendCategories = [];
    if (gender) {
      backendCategories = await fetchCategoriesFromBackend(gender);
      console.log('Backend categories count:', backendCategories.length);
      console.log('Backend categories:', backendCategories);
    } else {
      console.log('No gender available, skipping backend fetch');
    }

    // Step 3: Test items-fetchAll directly
    console.log('\n3. Testing items-fetchAll directly...');
    const itemsResult = await new Promise<any>((resolve, reject) => {
      Meteor.call('items-fetchAll', {}, (err: any, res: any) => {
        if (err) {
          console.error('Error calling items-fetchAll:', err);
          reject(err);
          return;
        }
        resolve(res);
      });
    });

    console.log('Items-fetchAll response structure:', Object.keys(itemsResult));
    console.log('Items-fetchAll success:', itemsResult.success);
    
    if (itemsResult.data && itemsResult.data.items) {
      console.log('Items count:', itemsResult.data.items.length);
      
      // Check first few items
      const sampleItems = itemsResult.data.items.slice(0, 3);
      console.log('\nSample items:');
      sampleItems.forEach((item: any, index: number) => {
        console.log(`Item ${index + 1}:`, {
          name: item.name,
          itemCategoryId: item.itemCategoryId,
          category: item.category,
          categoryName: item.categoryName
        });
      });
    }

    // Step 4: Test category mapping for sample IDs
    console.log('\n4. Testing category mapping for sample IDs...');
    const sampleIds = ['tops', 'bottoms', 'shoes'];
    
    if (backendCategories.length > 0) {
      console.log('Available category IDs from backend:');
      backendCategories.forEach(cat => {
        console.log(`  ${cat.id} → ${cat.name}`);
      });
    }

    // Step 5: Check if aggregation is working
    console.log('\n5. Checking aggregation results...');
    if (itemsResult.data && itemsResult.data.items) {
      const itemsWithCategories = itemsResult.data.items.filter((item: any) => item.category);
      const itemsWithCategoryIds = itemsResult.data.items.filter((item: any) => item.itemCategoryId);
      
      console.log(`Items with category object: ${itemsWithCategories.length}`);
      console.log(`Items with itemCategoryId: ${itemsWithCategoryIds.length}`);
      
      if (itemsWithCategories.length > 0) {
        console.log('Sample item with category object:', itemsWithCategories[0].category);
      }
      
      if (itemsWithCategoryIds.length > 0) {
        console.log('Sample itemCategoryIds:', itemsWithCategoryIds.slice(0, 5).map((item: any) => item.itemCategoryId));
      }
    }

    // Step 6: Test itemCategories-fetch directly
    console.log('\n6. Testing itemCategories-fetch directly...');
    const categoriesResult = await new Promise<any>((resolve, reject) => {
      Meteor.call('itemCategories-fetch', { gender: gender === 'Male' ? 'men' : gender === 'Female' ? 'women' : undefined }, (err: any, res: any) => {
        if (err) {
          console.error('Error calling itemCategories-fetch:', err);
          reject(err);
          return;
        }
        resolve(res);
      });
    });

    console.log('Categories-fetch response structure:', Object.keys(categoriesResult));
    if (categoriesResult.data && categoriesResult.data.itemCategories) {
      console.log('Categories count:', categoriesResult.data.itemCategories.length);
      console.log('Sample categories:');
      categoriesResult.data.itemCategories.slice(0, 3).forEach((cat: any, index: number) => {
        console.log(`Category ${index + 1}:`, {
          _id: cat._id,
          name: cat.name,
          mainCategory: cat.mainCategory,
          gender: cat.gender
        });
      });
    }

    console.log('\n✅ Debug completed! Check the logs above for insights.');

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  }
}

/**
 * Quick function to check if the backend aggregation is working
 */
export async function checkAggregation() {
  try {
    const result = await new Promise<any>((resolve, reject) => {
      Meteor.call('items-fetchAll', {}, (err: any, res: any) => {
        if (err) reject(err);
        else resolve(res);
      });
    });

    if (result.data && result.data.items) {
      const totalItems = result.data.items.length;
      const itemsWithCategoryObject = result.data.items.filter((item: any) => item.category && item.category.name).length;
      const itemsWithCategoryId = result.data.items.filter((item: any) => item.itemCategoryId).length;

      console.log('Aggregation Check Results:');
      console.log(`Total items: ${totalItems}`);
      console.log(`Items with category.name: ${itemsWithCategoryObject}`);
      console.log(`Items with itemCategoryId: ${itemsWithCategoryId}`);
      console.log(`Aggregation working: ${itemsWithCategoryObject > 0 ? 'YES' : 'NO'}`);

      return {
        totalItems,
        itemsWithCategoryObject,
        itemsWithCategoryId,
        aggregationWorking: itemsWithCategoryObject > 0
      };
    }

    return null;
  } catch (error) {
    console.error('Error checking aggregation:', error);
    return null;
  }
}
