import { createCategoryIdToNameMapping, mapCategoryIdToName, getCategoryDisplayName } from './categoryMapping';

/**
 * Test function to verify category mapping functionality
 * This can be called from the console or during development
 */
export async function testCategoryMapping() {
  console.log('🧪 Testing Category Mapping Functionality...\n');

  try {
    // Test 1: Create category mapping
    console.log('1. Creating category ID to name mapping...');
    const mapping = await createCategoryIdToNameMapping();
    console.log(`✅ Created mapping with ${mapping.size} categories`);
    
    // Log all mappings
    console.log('\n📋 All Category Mappings:');
    mapping.forEach((name, id) => {
      console.log(`  ${id} → ${name}`);
    });

    // Test 2: Test individual ID mapping
    console.log('\n2. Testing individual ID mapping...');
    const testIds = ['tops', 'bottoms', 'shoes', 'dresses', 'accessories', 'unknown-category'];
    
    for (const testId of testIds) {
      const mappedName = await mapCategoryIdToName(testId);
      console.log(`  ${testId} → ${mappedName}`);
    }

    // Test 3: Test with mock item objects
    console.log('\n3. Testing with mock item objects...');
    const mockItems = [
      { itemCategoryId: 'tops', name: 'Test Shirt' },
      { category: { name: 'Bottoms' }, name: 'Test Pants' },
      { itemCategoryId: 'unknown-id', name: 'Test Unknown' },
      { name: 'Test No Category' }
    ];

    for (const item of mockItems) {
      const categoryName = await getCategoryDisplayName(item);
      console.log(`  Item "${item.name}" → Category: ${categoryName}`);
    }

    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during testing:', error);
  }
}

/**
 * Quick test function that can be called from React components
 */
export async function quickCategoryTest() {
  try {
    const mapping = await createCategoryIdToNameMapping();
    console.log('Category mapping created successfully with', mapping.size, 'categories');
    return true;
  } catch (error) {
    console.error('Category mapping test failed:', error);
    return false;
  }
}
